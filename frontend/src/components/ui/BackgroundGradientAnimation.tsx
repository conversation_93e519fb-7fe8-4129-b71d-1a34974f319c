"use client";
import { cn } from "../../utils/cn";
import { useEffect, useRef, useState } from "react";

export const BackgroundGradientAnimation = ({
  gradientBackgroundStart = "rgb(108, 0, 162)",
  gradientBackgroundEnd = "rgb(0, 17, 82)",
  firstColor = "18, 113, 255",
  secondColor = "221, 74, 255",
  thirdColor = "100, 220, 255",
  fourthColor = "200, 50, 50",
  fifthColor = "180, 180, 50",
  pointerColor = "140, 100, 255",
  size = "80%",
  blendingValue = "hard-light",
  children,
  className,
  interactive = true,
  containerClassName,
}: {
  gradientBackgroundStart?: string;
  gradientBackgroundEnd?: string;
  firstColor?: string;
  secondColor?: string;
  thirdColor?: string;
  fourthColor?: string;
  fifthColor?: string;
  pointerColor?: string;
  size?: string;
  blendingValue?: string;
  children?: React.ReactNode;
  className?: string;
  interactive?: boolean;
  containerClassName?: string;
}) => {
  const interactiveRef = useRef<HTMLDivElement>(null);
  const [curX, setCurX] = useState(0);
  const [curY, setCurY] = useState(0);
  const [tgX, setTgX] = useState(0);
  const [tgY, setTgY] = useState(0);

  useEffect(() => {
    document.body.style.setProperty("--gradient-background-start", gradientBackgroundStart);
    document.body.style.setProperty("--gradient-background-end", gradientBackgroundEnd);
    document.body.style.setProperty("--first-color", firstColor);
    document.body.style.setProperty("--second-color", secondColor);
    document.body.style.setProperty("--third-color", thirdColor);
    document.body.style.setProperty("--fourth-color", fourthColor);
    document.body.style.setProperty("--fifth-color", fifthColor);
    document.body.style.setProperty("--pointer-color", pointerColor);
    document.body.style.setProperty("--size", size);
    document.body.style.setProperty("--blending-value", blendingValue);
  }, [gradientBackgroundStart, gradientBackgroundEnd, firstColor, secondColor, thirdColor, fourthColor, fifthColor, pointerColor, size, blendingValue]);

  useEffect(() => {
    const move = () => {
      if (!interactiveRef.current) return;
      setCurX(curX + (tgX - curX) / 20);
      setCurY(curY + (tgY - curY) / 20);
      interactiveRef.current.style.transform = `translate(${Math.round(curX)}px, ${Math.round(curY)}px)`;
    };
    move();
  }, [tgX, tgY, curX, curY]);

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (interactiveRef.current) {
      const rect = interactiveRef.current.parentElement!.getBoundingClientRect();
      setTgX(event.clientX - rect.left);
      setTgY(event.clientY - rect.top);
    }
  };

  const [isSafari, setIsSafari] = useState(false);
  useEffect(() => {
    setIsSafari(/^((?!chrome|android).)*safari/i.test(navigator.userAgent));
  }, []);

  return (
    <div
      className={cn(
        "h-screen w-screen relative overflow-hidden top-0 left-0 bg-[linear-gradient(40deg,var(--gradient-background-start),var(--gradient-background-end))]",
        containerClassName
      )}
      onMouseMove={interactive ? handleMouseMove : undefined}
    >
      <svg className="hidden">
        <defs>
          <filter id="blurMe">
            <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur" />
            <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8" result="goo" />
            <feBlend in="SourceGraphic" in2="goo" />
          </filter>
        </defs>
      </svg>
      <div className={cn("z-50 relative", className)}>{children}</div>
      <div
        className={cn(
          "gradients-container h-full w-full blur-lg absolute inset-0",
          isSafari ? "blur-2xl" : "[filter:url(#blurMe)_blur(40px)] [will-change:filter]"
        )}
      >
        <div
          className={cn(
            "[background:radial-gradient(circle_400px_at_calc(100px)_calc(100px),rgba(var(--first-color),0.4),transparent)]",
            "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2",
            "animate-moveInCircle",
            "[mix-blend-mode:var(--blending-value)]"
          )}
        ></div>
        <div
          className={cn(
            "[background:radial-gradient(circle_300px_at_calc(200px)_calc(0px),rgba(var(--second-color),0.4),transparent)]",
            "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2",
            "animate-moveHorizontal",
            "[mix-blend-mode:var(--blending-value)]"
          )}
        ></div>
        <div
          className={cn(
            "[background:radial-gradient(circle_300px_at_calc(0px)_calc(100px),rgba(var(--third-color),0.4),transparent)]",
            "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2",
            "animate-moveVertical",
            "[mix-blend-mode:var(--blending-value)]"
          )}
        ></div>
        <div
          className={cn(
            "[background:radial-gradient(circle_300px_at_calc(0px)_calc(200px),rgba(var(--fourth-color),0.4),transparent)]",
            "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2",
            "animate-moveHorizontal",
            "[mix-blend-mode:var(--blending-value)]"
          )}
        ></div>
        <div
          className={cn(
            "[background:radial-gradient(circle_400px_at_calc(150px)_calc(150px),rgba(var(--fifth-color),0.4),transparent)]",
            "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2",
            "animate-moveInCircle",
            "[mix-blend-mode:var(--blending-value)]"
          )}
        ></div>
        {interactive && (
          <div
            ref={interactiveRef}
            className={cn(
              "[background:radial-gradient(circle_100px_at_50%_50%,rgba(var(--pointer-color),0.8),transparent)]",
              "absolute top-1/2 left-1/2 w-[var(--size)] h-[var(--size)] -translate-x-1/2 -translate-y-1/2 pointer-events-none",
              "[mix-blend-mode:var(--blending-value)]",
              "transition duration-300"
            )}
          ></div>
        )}
      </div>
    </div>
  );
};
