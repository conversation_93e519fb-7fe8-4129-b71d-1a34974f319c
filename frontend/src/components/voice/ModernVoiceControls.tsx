import { useState, useEffect } from 'react'
import { Mi<PERSON>, MicOff, Phone, PhoneOff, Pause, Play, Volume2, VolumeX } from 'lucide-react'
import { cn } from '../../utils/cn'

interface ModernVoiceControlsProps {
  isConnected: boolean
  isChatActive: boolean
  isRecording: boolean
  isPlaying: boolean
  onStartChat: () => void
  onEndChat: () => void
  onToggleMute?: () => void
  onTogglePause?: () => void
  isMuted?: boolean
  isPaused?: boolean
  className?: string
}

export default function ModernVoiceControls({
  isConnected,
  isChatActive,
  isRecording,
  isPlaying,
  onStartChat,
  onEndChat,
  onToggleMute,
  onTogglePause,
  isMuted = false,
  isPaused = false,
  className
}: ModernVoiceControlsProps) {
  const [pulseKey, setPulseKey] = useState(0)

  // Trigger pulse animation when recording state changes
  useEffect(() => {
    if (isRecording) {
      setPulseKey(prev => prev + 1)
    }
  }, [isRecording])

  // Main conversation button (start/end)
  const ConversationButton = () => {
    if (!isChatActive) {
      return (
        <button
          onClick={onStartChat}
          disabled={!isConnected}
          className={cn(
            "relative w-20 h-20 rounded-full transition-all duration-300 transform",
            "flex items-center justify-center shadow-2xl",
            "focus:outline-none focus:ring-4 focus:ring-offset-2",
            isConnected
              ? "bg-gradient-to-r from-aura-600 to-sunbeam-600 hover:from-aura-700 hover:to-sunbeam-700 text-white hover:scale-110 focus:ring-aura-300"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          )}
        >
          <Phone className="w-8 h-8" />
          {isConnected && (
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-aura-600 to-sunbeam-600 opacity-30 animate-ping"></div>
          )}
        </button>
      )
    }

    return (
      <button
        onClick={onEndChat}
        className={cn(
          "relative w-20 h-20 rounded-full transition-all duration-300 transform",
          "flex items-center justify-center shadow-2xl",
          "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700",
          "text-white hover:scale-110 focus:outline-none focus:ring-4 focus:ring-red-300 focus:ring-offset-2"
        )}
      >
        <PhoneOff className="w-8 h-8" />
      </button>
    )
  }

  // Recording indicator with dynamic animation
  const RecordingIndicator = () => {
    if (!isChatActive) return null

    return (
      <div className="flex flex-col items-center space-y-3">
        {/* Visual recording indicator */}
        <div
          className={cn(
            "relative w-16 h-16 rounded-full transition-all duration-300",
            "flex items-center justify-center",
            isRecording
              ? "bg-gradient-to-r from-red-400 to-red-500 shadow-lg"
              : isPlaying
              ? "bg-gradient-to-r from-blue-400 to-blue-500 shadow-lg"
              : "bg-gradient-to-r from-gray-300 to-gray-400"
          )}
        >
          {isRecording ? (
            <>
              <div className="w-6 h-6 bg-white rounded-sm"></div>
              <div 
                key={pulseKey}
                className="absolute inset-0 rounded-full bg-red-400 opacity-50 animate-ping"
              ></div>
            </>
          ) : isPlaying ? (
            <Volume2 className="w-6 h-6 text-white" />
          ) : (
            <Mic className="w-6 h-6 text-white" />
          )}
        </div>

        {/* Status text */}
        <div className="text-center">
          <p className={cn(
            "text-sm font-medium transition-colors duration-300",
            isRecording ? "text-red-600" : isPlaying ? "text-blue-600" : "text-gray-600"
          )}>
            {isRecording ? "Listening..." : isPlaying ? "ORA is speaking" : "Ready to listen"}
          </p>
        </div>
      </div>
    )
  }

  // Secondary controls (mute, pause)
  const SecondaryControls = () => {
    if (!isChatActive) return null

    return (
      <div className="flex items-center space-x-4">
        {/* Mute button */}
        {onToggleMute && (
          <button
            onClick={onToggleMute}
            className={cn(
              "w-12 h-12 rounded-full transition-all duration-200 transform",
              "flex items-center justify-center shadow-lg hover:scale-105",
              "focus:outline-none focus:ring-2 focus:ring-offset-2",
              isMuted
                ? "bg-red-100 text-red-600 hover:bg-red-200 focus:ring-red-300"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-300"
            )}
          >
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </button>
        )}

        {/* Pause button */}
        {onTogglePause && (
          <button
            onClick={onTogglePause}
            className={cn(
              "w-12 h-12 rounded-full transition-all duration-200 transform",
              "flex items-center justify-center shadow-lg hover:scale-105",
              "focus:outline-none focus:ring-2 focus:ring-offset-2",
              isPaused
                ? "bg-yellow-100 text-yellow-600 hover:bg-yellow-200 focus:ring-yellow-300"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-300"
            )}
          >
            {isPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
          </button>
        )}
      </div>
    )
  }

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center space-x-2 mb-6">
      <div className={cn(
        "w-3 h-3 rounded-full transition-colors duration-300",
        isConnected ? "bg-green-500" : "bg-red-500"
      )} />
      <span className={cn(
        "text-sm font-medium transition-colors duration-300",
        isConnected ? "text-green-600" : "text-red-600"
      )}>
        {isConnected ? "Connected to ORA" : "Disconnected"}
      </span>
    </div>
  )

  return (
    <div className={cn(
      "flex flex-col items-center justify-center space-y-8 p-8",
      className
    )}>
      {/* Connection status */}
      <ConnectionStatus />

      {/* Recording indicator (only when chat is active) */}
      <RecordingIndicator />

      {/* Main conversation button */}
      <ConversationButton />

      {/* Secondary controls */}
      <SecondaryControls />

      {/* Help text */}
      <div className="text-center max-w-md">
        {!isChatActive ? (
          <p className="text-gray-500 text-sm">
            {isConnected 
              ? "Tap the button above to start your conversation with ORA"
              : "Connecting to ORA..."
            }
          </p>
        ) : (
          <p className="text-gray-500 text-sm">
            Speak naturally - ORA will listen and respond. Tap the red button to end the conversation.
          </p>
        )}
      </div>
    </div>
  )
}
