import { useEffect, useMemo } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { BackgroundGradientAnimation } from '../components/ui/BackgroundGradientAnimation'
import ModernVoiceControls from '../components/voice/ModernVoiceControls'
import { blendEmotionColors } from '../utils/emotionColors'

export default function ChatPage() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Get current emotions from the latest message for dynamic background
  const currentEmotions = useMemo(() => {
    if (messages.length === 0) return {}

    // Get emotions from the most recent message
    const latestMessage = messages[messages.length - 1]
    return latestMessage?.emotions || {}
  }, [messages])

  // Get emotion-based color scheme
  const emotionColorScheme = useMemo(() => {
    return blendEmotionColors(currentEmotions)
  }, [currentEmotions])

  // Convert emotion colors to the format expected by BackgroundGradientAnimation
  const getEmotionBasedColors = () => {
    if (Object.keys(currentEmotions).length === 0) {
      // Default ORA brand colors when no emotions
      return {
        firstColor: "255, 213, 169", // Ardent
        secondColor: "218, 113, 52", // Sunbeam
        thirdColor: "133, 38, 22", // Aura
        fourthColor: "232, 145, 84", // Bliss
        fifthColor: "255, 237, 213", // Light ardent
        pointerColor: "133, 38, 22", // Aura
      }
    }

    return {
      firstColor: emotionColorScheme.primary,
      secondColor: emotionColorScheme.secondary,
      thirdColor: emotionColorScheme.accent,
      fourthColor: emotionColorScheme.secondary,
      fifthColor: emotionColorScheme.primary,
      pointerColor: emotionColorScheme.accent,
    }
  }

  const emotionColors = getEmotionBasedColors()

  // Debug: Log emotion changes for development
  useEffect(() => {
    if (Object.keys(currentEmotions).length > 0) {
      console.log('🎨 Emotion-based colors updated:', {
        emotions: currentEmotions,
        dominantEmotion: Object.entries(currentEmotions).sort(([,a], [,b]) => b - a)[0],
        colors: emotionColors
      })
    }
  }, [currentEmotions, emotionColors])

  useEffect(() => {
    // Auto-connect when component mounts
    if (!isConnected && !isConnecting) {
      handleConnect()
    }

    return () => {
      // Cleanup handled by audio service
    }
  }, [])

  const handleConnect = async () => {
    try {
      await connect()
      addToast({
        type: 'success',
        title: 'Connected',
        message: 'Successfully connected to ORA'
      })
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to ORA'
      })
    }
  }

  const handleStartChat = async () => {
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()

      addToast({
        type: 'success',
        title: 'Conversation Started',
        message: 'You can now speak with ORA'
      })
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Failed to Start',
        message: 'Could not access microphone or start conversation'
      })
    }
  }

  const handleEndChat = () => {
    endChat()

    addToast({
      type: 'info',
      title: 'Conversation Ended',
      message: 'Your conversation with ORA has ended'
    })
  }

  return (
    <BackgroundGradientAnimation
      gradientBackgroundStart="rgb(255, 255, 255)"
      gradientBackgroundEnd="rgb(248, 250, 252)"
      firstColor={emotionColors.firstColor}
      secondColor={emotionColors.secondColor}
      thirdColor={emotionColors.thirdColor}
      fourthColor={emotionColors.fourthColor}
      fifthColor={emotionColors.fifthColor}
      pointerColor={emotionColors.pointerColor}
      size="90%"
      blendingValue="normal"
      interactive={true}
      containerClassName="fixed inset-0"
    >
      {/* Main content overlay */}
      <div className="relative z-10 h-screen w-screen flex flex-col">
        {/* Top status bar - minimal and elegant */}
        <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-20">
          <div className="bg-white/80 backdrop-blur-md rounded-full px-6 py-3 shadow-lg border border-white/20">
            <div className="flex items-center space-x-3">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm font-medium text-gray-700">
                {isConnecting ? 'Connecting...' : isConnected ? 'ORA' : 'Disconnected'}
              </span>
              {!isConnected && !isConnecting && (
                <button
                  onClick={handleConnect}
                  className="text-xs bg-aura-600 text-white px-3 py-1 rounded-full hover:bg-aura-700 transition-colors"
                >
                  Reconnect
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Center voice controls */}
        <div className="flex-1 flex items-center justify-center">
          <ModernVoiceControls
            isConnected={isConnected}
            isChatActive={isChatActive}
            isRecording={isRecording}
            isPlaying={isPlaying}
            onStartChat={handleStartChat}
            onEndChat={handleEndChat}
            className="bg-white/60 backdrop-blur-md rounded-3xl shadow-2xl border border-white/30"
          />
        </div>

        {/* Bottom emotion indicator - enhanced */}
        {Object.keys(currentEmotions).length > 0 && (
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
            <div className="bg-white/80 backdrop-blur-md rounded-full px-6 py-3 shadow-lg border border-white/20">
              <div className="flex items-center space-x-3">
                <div
                  className="w-4 h-4 rounded-full animate-pulse"
                  style={{ backgroundColor: `rgb(${emotionColors.firstColor})` }}
                />
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-700">
                    {Object.entries(currentEmotions)
                      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Neutral'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {Object.entries(currentEmotions).length > 1 &&
                      `+${Object.entries(currentEmotions).length - 1} more`}
                  </span>
                </div>
                <div className="text-xs text-gray-400">
                  {(Object.entries(currentEmotions)
                    .sort(([,a], [,b]) => b - a)[0]?.[1] * 100 || 0).toFixed(0)}%
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error notification */}
        {connectionError && (
          <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-30">
            <div className="bg-red-50/90 backdrop-blur-md border border-red-200 rounded-xl p-4 shadow-lg">
              <p className="text-sm text-red-800">{connectionError}</p>
            </div>
          </div>
        )}
      </div>
    </BackgroundGradientAnimation>
  )
}
