export interface EmotionColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  intensity: number;
}

export interface AnimationPattern {
  speed: 'slow' | 'normal' | 'fast';
  intensity: 'low' | 'medium' | 'high';
  pattern: 'circular' | 'horizontal' | 'vertical' | 'chaotic';
}

// Comprehensive emotion to color mapping for all Hume emotions with beautiful pastel colors
export const emotionColorMap: Record<string, EmotionColorScheme> = {
  // Positive High-Energy Emotions
  joy: {
    primary: "144, 238, 144", // Light green
    secondary: "173, 255, 173", // Pale green
    accent: "255, 255, 224", // Light yellow
    background: "248, 255, 248", // Very light green
    intensity: 0.8,
  },
  excitement: {
    primary: "255, 182, 193", // Light pink
    secondary: "255, 218, 185", // Peach
    accent: "255, 240, 245", // Lavender blush
    background: "255, 250, 250", // Snow
    intensity: 0.9,
  },
  ecstasy: {
    primary: "255, 192, 203", // Pink
    secondary: "255, 228, 225", // Misty rose
    accent: "255, 240, 245", // Lavender blush
    background: "255, 248, 248", // Very light pink
    intensity: 0.95,
  },
  triumph: {
    primary: "255, 215, 0", // Gold
    secondary: "255, 239, 213", // <PERSON>ya whip
    accent: "255, 250, 205", // <PERSON> chiffon
    background: "255, 253, 240", // Very light gold
    intensity: 0.85,
  },
  amusement: {
    primary: "173, 216, 230", // Light blue
    secondary: "224, 255, 255", // Light cyan
    accent: "240, 248, 255", // Alice blue
    background: "248, 248, 255", // Ghost white
    intensity: 0.7,
  },

  // Calm and Peaceful Emotions
  calmness: {
    primary: "176, 224, 230", // Powder blue
    secondary: "230, 230, 250", // Lavender
    accent: "248, 248, 255", // Ghost white
    background: "245, 245, 245", // White smoke
    intensity: 0.4,
  },
  relief: {
    primary: "152, 251, 152", // Pale green
    secondary: "240, 255, 240", // Honeydew
    accent: "245, 255, 250", // Mint cream
    background: "248, 255, 248", // Very light green
    intensity: 0.5,
  },
  contentment: {
    primary: "221, 160, 221", // Plum
    secondary: "238, 221, 238", // Thistle
    accent: "250, 240, 230", // Linen
    background: "255, 248, 248", // Very light pink
    intensity: 0.6,
  },
  satisfaction: {
    primary: "255, 228, 196", // Bisque
    secondary: "255, 239, 213", // Papaya whip
    accent: "255, 245, 238", // Seashell
    background: "255, 250, 240", // Floral white
    intensity: 0.7,
  },
  concentration: {
    primary: "230, 230, 250", // Lavender
    secondary: "240, 248, 255", // Alice blue
    accent: "248, 248, 255", // Ghost white
    background: "245, 245, 245", // White smoke
    intensity: 0.5,
  },
  contemplation: {
    primary: "216, 191, 216", // Thistle
    secondary: "221, 160, 221", // Plum
    accent: "238, 221, 238", // Light thistle
    background: "250, 240, 230", // Linen
    intensity: 0.4,
  },

  // Love and Affection Emotions
  love: {
    primary: "255, 182, 193", // Light pink
    secondary: "255, 228, 225", // Misty rose
    accent: "255, 240, 245", // Lavender blush
    background: "255, 248, 248", // Very light pink
    intensity: 0.8,
  },
  adoration: {
    primary: "255, 192, 203", // Pink
    secondary: "255, 218, 185", // Peach
    accent: "255, 228, 225", // Misty rose
    background: "255, 250, 250", // Snow
    intensity: 0.7,
  },
  romance: {
    primary: "255, 105, 180", // Hot pink (softened)
    secondary: "255, 182, 193", // Light pink
    accent: "255, 228, 225", // Misty rose
    background: "255, 240, 245", // Lavender blush
    intensity: 0.75,
  },
  affection: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "255, 239, 213", // Papaya whip
    background: "255, 245, 238", // Seashell
    intensity: 0.6,
  },

  // Challenging Emotions - Soothing colors to balance
  anger: {
    primary: "255, 160, 122", // Light salmon
    secondary: "255, 218, 185", // Peach
    accent: "255, 228, 196", // Bisque
    background: "255, 245, 238", // Seashell
    intensity: 0.6,
  },
  frustration: {
    primary: "255, 182, 193", // Light pink
    secondary: "255, 228, 225", // Misty rose
    accent: "240, 255, 240", // Honeydew (calming)
    background: "255, 248, 248", // Very light pink
    intensity: 0.5,
  },

  // Sadness and Melancholy - Gentle purples and blues
  sadness: {
    primary: "176, 196, 222", // Light steel blue
    secondary: "230, 230, 250", // Lavender
    accent: "240, 248, 255", // Alice blue
    background: "248, 248, 255", // Ghost white
    intensity: 0.5,
  },
  melancholy: {
    primary: "221, 160, 221", // Plum
    secondary: "216, 191, 216", // Thistle
    accent: "230, 230, 250", // Lavender
    background: "250, 240, 230", // Linen
    intensity: 0.4,
  },
  grief: {
    primary: "192, 192, 192", // Silver
    secondary: "211, 211, 211", // Light gray
    accent: "230, 230, 250", // Lavender
    background: "248, 248, 255", // Ghost white
    intensity: 0.3,
  },
  pain: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "240, 255, 240", // Honeydew
    background: "255, 245, 238", // Seashell
    intensity: 0.4,
  },
  empathicPain: {
    primary: "255, 228, 225", // Misty rose
    secondary: "255, 239, 213", // Papaya whip
    accent: "240, 248, 255", // Alice blue
    background: "255, 248, 248", // Very light pink
    intensity: 0.5,
  },

  // Fear and Anxiety - Warm, comforting colors
  fear: {
    primary: "255, 228, 196", // Bisque
    secondary: "255, 239, 213", // Papaya whip
    accent: "240, 255, 240", // Honeydew (grounding)
    background: "255, 250, 240", // Floral white
    intensity: 0.5,
  },
  anxiety: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "240, 248, 255", // Alice blue (calming)
    background: "255, 245, 238", // Seashell
    intensity: 0.6,
  },
  worry: {
    primary: "255, 239, 213", // Papaya whip
    secondary: "255, 245, 238", // Seashell
    accent: "240, 255, 255", // Light cyan (soothing)
    background: "255, 250, 240", // Floral white
    intensity: 0.4,
  },
  distress: {
    primary: "255, 228, 225", // Misty rose
    secondary: "255, 239, 213", // Papaya whip
    accent: "230, 230, 250", // Lavender
    background: "255, 248, 248", // Very light pink
    intensity: 0.5,
  },

  // Wonder and Curiosity
  awe: {
    primary: "230, 230, 250", // Lavender
    secondary: "240, 248, 255", // Alice blue
    accent: "255, 240, 245", // Lavender blush
    background: "248, 248, 255", // Ghost white
    intensity: 0.8,
  },
  interest: {
    primary: "173, 216, 230", // Light blue
    secondary: "224, 255, 255", // Light cyan
    accent: "240, 248, 255", // Alice blue
    background: "245, 245, 245", // White smoke
    intensity: 0.7,
  },
  admiration: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "255, 239, 213", // Papaya whip
    background: "255, 245, 238", // Seashell
    intensity: 0.75,
  },
  entrancement: {
    primary: "221, 160, 221", // Plum
    secondary: "230, 230, 250", // Lavender
    accent: "240, 248, 255", // Alice blue
    background: "250, 240, 230", // Linen
    intensity: 0.8,
  },
  aestheticAppreciation: {
    primary: "255, 192, 203", // Pink
    secondary: "255, 218, 185", // Peach
    accent: "230, 230, 250", // Lavender
    background: "255, 240, 245", // Lavender blush
    intensity: 0.7,
  },

  // Surprise emotions
  surprisePositive: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "255, 240, 245", // Lavender blush
    background: "255, 245, 238", // Seashell
    intensity: 0.8,
  },
  surpriseNegative: {
    primary: "230, 230, 250", // Lavender
    secondary: "240, 248, 255", // Alice blue
    accent: "255, 228, 225", // Misty rose
    background: "248, 248, 255", // Ghost white
    intensity: 0.6,
  },

  // Complex emotions
  envy: {
    primary: "152, 251, 152", // Pale green
    secondary: "240, 255, 240", // Honeydew
    accent: "255, 228, 196", // Bisque (balancing)
    background: "248, 255, 248", // Very light green
    intensity: 0.4,
  },
  guilt: {
    primary: "255, 228, 225", // Misty rose
    secondary: "255, 239, 213", // Papaya whip
    accent: "240, 255, 240", // Honeydew
    background: "255, 248, 248", // Very light pink
    intensity: 0.5,
  },
  shame: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "230, 230, 250", // Lavender
    background: "255, 245, 238", // Seashell
    intensity: 0.4,
  },
  pride: {
    primary: "255, 215, 0", // Gold (softened)
    secondary: "255, 228, 196", // Bisque
    accent: "255, 239, 213", // Papaya whip
    background: "255, 250, 240", // Floral white
    intensity: 0.7,
  },
  doubt: {
    primary: "211, 211, 211", // Light gray
    secondary: "230, 230, 250", // Lavender
    accent: "240, 248, 255", // Alice blue
    background: "248, 248, 255", // Ghost white
    intensity: 0.4,
  },
  confusion: {
    primary: "230, 230, 250", // Lavender
    secondary: "240, 248, 255", // Alice blue
    accent: "255, 228, 196", // Bisque
    background: "248, 248, 255", // Ghost white
    intensity: 0.5,
  },
  realization: {
    primary: "255, 239, 213", // Papaya whip
    secondary: "255, 245, 238", // Seashell
    accent: "240, 248, 255", // Alice blue
    background: "255, 250, 240", // Floral white
    intensity: 0.6,
  },
  determination: {
    primary: "176, 224, 230", // Powder blue
    secondary: "173, 216, 230", // Light blue
    accent: "255, 228, 196", // Bisque
    background: "240, 248, 255", // Alice blue
    intensity: 0.8,
  },

  // Social emotions
  sympathy: {
    primary: "255, 228, 225", // Misty rose
    secondary: "255, 239, 213", // Papaya whip
    accent: "240, 255, 240", // Honeydew
    background: "255, 248, 248", // Very light pink
    intensity: 0.6,
  },
  embarrassment: {
    primary: "255, 182, 193", // Light pink
    secondary: "255, 228, 225", // Misty rose
    accent: "255, 239, 213", // Papaya whip
    background: "255, 240, 245", // Lavender blush
    intensity: 0.5,
  },
  awkwardness: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "240, 248, 255", // Alice blue
    background: "255, 245, 238", // Seashell
    intensity: 0.4,
  },
  disappointment: {
    primary: "192, 192, 192", // Silver
    secondary: "230, 230, 250", // Lavender
    accent: "240, 248, 255", // Alice blue
    background: "248, 248, 255", // Ghost white
    intensity: 0.4,
  },

  // Miscellaneous emotions
  boredom: {
    primary: "211, 211, 211", // Light gray
    secondary: "230, 230, 250", // Lavender
    accent: "255, 228, 196", // Bisque
    background: "245, 245, 245", // White smoke
    intensity: 0.3,
  },
  tiredness: {
    primary: "230, 230, 250", // Lavender
    secondary: "240, 248, 255", // Alice blue
    accent: "255, 228, 196", // Bisque
    background: "248, 248, 255", // Ghost white
    intensity: 0.3,
  },
  craving: {
    primary: "255, 218, 185", // Peach
    secondary: "255, 228, 196", // Bisque
    accent: "255, 239, 213", // Papaya whip
    background: "255, 245, 238", // Seashell
    intensity: 0.6,
  },
  desire: {
    primary: "255, 192, 203", // Pink
    secondary: "255, 218, 185", // Peach
    accent: "255, 228, 196", // Bisque
    background: "255, 240, 245", // Lavender blush
    intensity: 0.7,
  },
  disgust: {
    primary: "192, 192, 192", // Silver
    secondary: "211, 211, 211", // Light gray
    accent: "240, 255, 240", // Honeydew (cleansing)
    background: "245, 245, 245", // White smoke
    intensity: 0.3,
  },
  contempt: {
    primary: "176, 196, 222", // Light steel blue
    secondary: "211, 211, 211", // Light gray
    accent: "230, 230, 250", // Lavender
    background: "248, 248, 255", // Ghost white
    intensity: 0.4,
  },
  horror: {
    primary: "255, 228, 225", // Misty rose
    secondary: "255, 239, 213", // Papaya whip
    accent: "240, 255, 240", // Honeydew (calming)
    background: "255, 248, 248", // Very light pink
    intensity: 0.5,
  },
  nostalgia: {
    primary: "221, 160, 221", // Plum
    secondary: "230, 230, 250", // Lavender
    accent: "255, 228, 196", // Bisque
    background: "250, 240, 230", // Linen
    intensity: 0.6,
  },

  // Default fallback
  default: {
    primary: "255, 213, 169", // Ardent
    secondary: "218, 113, 52", // Sunbeam
    accent: "133, 38, 22", // Aura
    background: "255, 251, 235", // Very light cream
    intensity: 0.5,
  },
};

// Animation patterns based on emotions
export const emotionAnimationMap: Record<string, AnimationPattern> = {
  // High energy emotions
  joy: { speed: 'fast', intensity: 'high', pattern: 'chaotic' },
  excitement: { speed: 'fast', intensity: 'high', pattern: 'chaotic' },
  surprise: { speed: 'fast', intensity: 'medium', pattern: 'circular' },
  
  // Calm emotions
  calmness: { speed: 'slow', intensity: 'low', pattern: 'horizontal' },
  peace: { speed: 'slow', intensity: 'low', pattern: 'vertical' },
  serenity: { speed: 'slow', intensity: 'low', pattern: 'circular' },
  relaxation: { speed: 'slow', intensity: 'low', pattern: 'horizontal' },
  
  // Moderate energy emotions
  happiness: { speed: 'normal', intensity: 'medium', pattern: 'circular' },
  love: { speed: 'normal', intensity: 'medium', pattern: 'circular' },
  affection: { speed: 'normal', intensity: 'medium', pattern: 'horizontal' },
  
  // Agitated emotions (but with calming patterns)
  anger: { speed: 'normal', intensity: 'medium', pattern: 'horizontal' },
  frustration: { speed: 'normal', intensity: 'medium', pattern: 'vertical' },
  anxiety: { speed: 'normal', intensity: 'medium', pattern: 'circular' },
  
  // Low energy emotions
  sadness: { speed: 'slow', intensity: 'low', pattern: 'vertical' },
  melancholy: { speed: 'slow', intensity: 'low', pattern: 'vertical' },
  grief: { speed: 'slow', intensity: 'low', pattern: 'horizontal' },
  
  // Default
  default: { speed: 'normal', intensity: 'medium', pattern: 'circular' },
};

/**
 * Get color scheme for the dominant emotion
 */
export function getEmotionColorScheme(emotions: Record<string, number>): EmotionColorScheme {
  if (!emotions || Object.keys(emotions).length === 0) {
    return emotionColorMap.default;
  }

  // Find the emotion with the highest score
  const dominantEmotion = Object.entries(emotions)
    .sort(([,a], [,b]) => b - a)[0];

  if (!dominantEmotion || dominantEmotion[1] < 0.1) {
    return emotionColorMap.default;
  }

  const emotionName = dominantEmotion[0].toLowerCase();
  return emotionColorMap[emotionName] || emotionColorMap.default;
}

/**
 * Get animation pattern for the dominant emotion
 */
export function getEmotionAnimationPattern(emotions: Record<string, number>): AnimationPattern {
  if (!emotions || Object.keys(emotions).length === 0) {
    return emotionAnimationMap.default;
  }

  const dominantEmotion = Object.entries(emotions)
    .sort(([,a], [,b]) => b - a)[0];

  if (!dominantEmotion || dominantEmotion[1] < 0.1) {
    return emotionAnimationMap.default;
  }

  const emotionName = dominantEmotion[0].toLowerCase();
  return emotionAnimationMap[emotionName] || emotionAnimationMap.default;
}

/**
 * Blend multiple emotion colors based on their intensities
 */
export function blendEmotionColors(emotions: Record<string, number>): EmotionColorScheme {
  if (!emotions || Object.keys(emotions).length === 0) {
    return emotionColorMap.default;
  }

  // Get top 3 emotions
  const topEmotions = Object.entries(emotions)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .filter(([,score]) => score > 0.1);

  if (topEmotions.length === 0) {
    return emotionColorMap.default;
  }

  if (topEmotions.length === 1) {
    const emotionName = topEmotions[0][0].toLowerCase();
    return emotionColorMap[emotionName] || emotionColorMap.default;
  }

  // For multiple emotions, use the dominant one but adjust intensity
  const dominantEmotion = topEmotions[0];
  const emotionName = dominantEmotion[0].toLowerCase();
  const baseScheme = emotionColorMap[emotionName] || emotionColorMap.default;

  // Adjust intensity based on emotion strength
  const adjustedIntensity = Math.min(0.9, baseScheme.intensity * dominantEmotion[1]);

  return {
    ...baseScheme,
    intensity: adjustedIntensity,
  };
}
